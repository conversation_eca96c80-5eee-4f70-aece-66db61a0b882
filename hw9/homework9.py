############################################################
# CIS 521: Homework 9
############################################################

############################################################
# Imports
############################################################

# Include your imports here, if any are used.
import homework9_data as data

############################################################

student_name = "Type your full name here."

############################################################
# Section 1: Perceptrons
############################################################


class BinaryPerceptron(object):

    def __init__(self, examples, iterations):
        pass

    def predict(self, x):
        pass


class MulticlassPerceptron(object):

    def __init__(self, examples, iterations):
        pass
    
    def predict(self, x):
        pass

############################################################
# Section 2: Applications
############################################################


class IrisClassifier(object):

    def __init__(self, data):
        pass

    def classify(self, instance):
        pass


class DigitClassifier(object):

    def __init__(self, data):
        pass

    def classify(self, instance):
        pass


class BiasClassifier(object):

    def __init__(self, data):
        pass

    def classify(self, instance):
        pass


class MysteryClassifier1(object):

    def __init__(self, data):
        pass

    def classify(self, instance):
        pass


class MysteryClassifier2(object):

    def __init__(self, data):
        pass

    def classify(self, instance):
        pass

############################################################
# Section 3: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
Type your response here.
Your response may span multiple lines.
Do not include these instructions in your response.
"""

feedback_question_2 = """
Type your response here.
Your response may span multiple lines.
Do not include these instructions in your response.
"""

feedback_question_3 = """
Type your response here.
Your response may span multiple lines.
Do not include these instructions in your response.
"""
