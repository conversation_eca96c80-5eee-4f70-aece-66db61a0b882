possible_worlds = set()
possible_pit_or_wampa_rooms = self.KB.all_rooms - self.KB.walls - self.KB.safe_rooms

# Generate all combinations of pit rooms (including no pits)
for num_pits in range(len(possible_pit_or_wampa_rooms) + 1):
    for pit_rooms in combinations(possible_pit_or_wampa_rooms, num_pits):
        # For each combination of pits, consider all possible wampa locations
        # including no wampa (represented by empty tuple)
        for wampa_room in possible_pit_or_wampa_rooms:
            possible_worlds.add((pit_rooms, wampa_room))
        # Also consider the case where there is no wampa
        possible_worlds.add((pit_rooms, tuple()))

return possible_worlds